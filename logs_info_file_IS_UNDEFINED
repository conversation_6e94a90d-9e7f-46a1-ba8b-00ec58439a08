2025-08-09 16:21:21.419|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-08-09 16:21:21.449|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-qingsong with PID 10608 (D:\work\projects\businesses-gateway\target\classes started by LENOVO in D:\work\projects\businesses-gateway)
2025-08-09 16:21:21.450|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: dev
2025-08-09 16:21:22.660|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-09 16:21:22.665|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-09 16:21:22.700|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-08-09 16:21:22.862|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-08-09 16:21:22.864|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-08-09 16:21:23.024|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=09842346-7c05-313c-8e8f-1914436bc553
2025-08-09 16:21:23.088|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://**********:9100!
2025-08-09 16:21:23.092|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://**********:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-08-09 16:21:23.639|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:21:23.644|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:21:23.672|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:21:23.765|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$b0818352] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:21:23.789|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:21:23.790|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:21:23.792|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:21:25.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-08-09 16:21:25.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-08-09 16:21:25.286|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-08-09 16:21:25.286|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-08-09 16:21:25.287|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-08-09 16:21:25.287|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-08-09 16:21:25.287|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-08-09 16:21:25.288|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-08-09 16:21:25.288|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-08-09 16:21:25.288|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-08-09 16:21:25.288|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-08-09 16:21:25.289|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-08-09 16:21:25.289|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-09 16:21:25.531|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:21:25.801|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:21:26.300|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:21:26.797|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:21:26.798|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties]
2025-08-09 16:21:27.301|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:21:27.301|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: bizconfig, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties].
2025-08-09 16:21:27.302|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:21:27.937|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:21:28.006|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:21:28.065|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:21:28.113|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:21:28.130|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:21:28.233|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 16:21:28.233|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 16:21:28.241|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 16:21:28.242|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 16:21:28.793|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-08-09 16:21:28.797|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-09 16:21:28.953|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-09 16:21:29.129|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-09 16:21:29.234|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-09 16:21:29.619|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-09 16:21:30.258|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-08-09 16:21:30.670|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-08-09 16:21:30.758|INFO| N/A||main|c.a.c.n.r.NacosServiceRegistry:62|nacos registry, businesses-gateway 198.18.0.1:9000 register finished
2025-08-09 16:21:30.922|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 10.678 seconds (JVM running for 12.24)
2025-08-09 16:21:30.923|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-08-09 16:21:30.923|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-08-09 16:21:30.927|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-08-09 16:21:30.991|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-08-09 16:21:43.879|ERROR| N/A||reactor-http-nio-2|r.n.channel.ChannelOperationsHandler:314|[id:0bd9ff93-1, L:/127.0.0.1:9000 - R:/127.0.0.1:53694] Error was received while reading the incoming data. The connection will be closed.
java.lang.NoSuchMethodError: org.springframework.http.server.reactive.ServerHttpRequest$Builder.header(Ljava/lang/String;Ljava/lang/String;)Lorg/springframework/http/server/reactive/ServerHttpRequest$Builder;
	at com.yxt.safecenter.auth.sdk.core.transform.core.DefaultParameterTransformer.lambda$transformRequestInternal$4(DefaultParameterTransformer.java:174)
	at java.util.Map.forEach(Map.java:630)
	at com.yxt.safecenter.auth.sdk.core.transform.core.DefaultParameterTransformer.transformRequestInternal(DefaultParameterTransformer.java:172)
	at com.yxt.safecenter.auth.sdk.core.transform.core.DefaultParameterTransformer.lambda$transformRequest$0(DefaultParameterTransformer.java:69)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:125)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:100)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
	at reactor.core.publisher.MonoReduceSeed$ReduceSeedSubscriber.onComplete(MonoReduceSeed.java:165)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:397)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:150)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:397)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:150)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:397)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:397)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:142)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:416)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:556)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:94)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:253)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-08-09 16:21:43.882|WARN| N/A||reactor-http-nio-2|reactor.netty.channel.FluxReceive:289|[id:0bd9ff93-1, L:/127.0.0.1:9000 ! R:/127.0.0.1:53694] An exception has been observed post termination, use DEBUG level to see the full stack: java.lang.NoSuchMethodError: org.springframework.http.server.reactive.ServerHttpRequest$Builder.header(Ljava/lang/String;Ljava/lang/String;)Lorg/springframework/http/server/reactive/ServerHttpRequest$Builder;
2025-08-09 16:23:27.531|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:25:27.544|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:26:25.805|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:27:27.547|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:28:50.629|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:74|De-registering from Nacos Server now...
2025-08-09 16:28:50.635|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:93|De-registration finished.
2025-08-09 16:32:35.526|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-08-09 16:32:35.553|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-qingsong with PID 33696 (D:\work\projects\businesses-gateway\target\classes started by LENOVO in D:\work\projects\businesses-gateway)
2025-08-09 16:32:35.554|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: dev
2025-08-09 16:32:36.662|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-09 16:32:36.665|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-09 16:32:36.701|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-08-09 16:32:36.854|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-08-09 16:32:36.857|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-08-09 16:32:37.011|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=04f68548-48b0-35b7-8dd6-06c9bd2a42ee
2025-08-09 16:32:37.079|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://**********:9100!
2025-08-09 16:32:37.082|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://**********:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-08-09 16:32:37.667|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:32:37.672|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:32:37.701|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:32:37.787|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$6e05aa6c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:32:37.804|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:32:37.805|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:32:37.807|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:32:39.390|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-08-09 16:32:39.391|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-08-09 16:32:39.391|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-08-09 16:32:39.391|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-08-09 16:32:39.391|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-08-09 16:32:39.392|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-08-09 16:32:39.392|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-08-09 16:32:39.392|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-08-09 16:32:39.392|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-08-09 16:32:39.392|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-08-09 16:32:39.393|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-08-09 16:32:39.393|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-08-09 16:32:39.393|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-09 16:32:39.632|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:32:39.880|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:32:40.377|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:32:40.879|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:32:40.881|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties]
2025-08-09 16:32:41.387|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:32:41.388|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: bizconfig, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties].
2025-08-09 16:32:41.388|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:32:41.984|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:32:42.054|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:32:42.112|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:32:42.162|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:32:42.189|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:32:42.267|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 16:32:42.267|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 16:32:42.275|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 16:32:42.275|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 16:32:42.815|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-08-09 16:32:42.818|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-09 16:32:42.980|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-09 16:32:43.151|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-09 16:32:43.256|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-09 16:32:43.638|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-09 16:32:43.932|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-08-09 16:32:44.384|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-08-09 16:32:44.466|INFO| N/A||main|c.a.c.n.r.NacosServiceRegistry:62|nacos registry, businesses-gateway 198.18.0.1:9000 register finished
2025-08-09 16:32:44.610|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 10.036 seconds (JVM running for 11.137)
2025-08-09 16:32:44.611|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-08-09 16:32:44.611|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-08-09 16:32:44.616|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-08-09 16:32:44.675|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-08-09 16:34:41.624|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:35:14.528|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:74|De-registering from Nacos Server now...
2025-08-09 16:35:14.535|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:93|De-registration finished.
2025-08-09 16:35:20.429|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-08-09 16:35:20.454|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-qingsong with PID 10592 (D:\work\projects\businesses-gateway\target\classes started by LENOVO in D:\work\projects\businesses-gateway)
2025-08-09 16:35:20.455|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: dev
2025-08-09 16:35:21.589|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-09 16:35:21.592|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-09 16:35:21.623|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-08-09 16:35:21.774|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-08-09 16:35:21.776|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-08-09 16:35:21.928|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=09842346-7c05-313c-8e8f-1914436bc553
2025-08-09 16:35:21.992|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://**********:9100!
2025-08-09 16:35:21.995|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://**********:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-08-09 16:35:22.549|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:35:22.554|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:35:22.588|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:35:22.675|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$a47148a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:35:22.691|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:35:22.693|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:35:22.695|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 16:35:24.178|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-08-09 16:35:24.178|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-08-09 16:35:24.178|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-08-09 16:35:24.179|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-08-09 16:35:24.179|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-08-09 16:35:24.179|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-08-09 16:35:24.179|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-08-09 16:35:24.179|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-08-09 16:35:24.179|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-08-09 16:35:24.179|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-08-09 16:35:24.181|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-08-09 16:35:24.181|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-08-09 16:35:24.181|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-09 16:35:24.396|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:35:24.673|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:35:25.180|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:35:25.669|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:35:25.670|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties]
2025-08-09 16:35:26.172|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 16:35:26.174|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: bizconfig, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties].
2025-08-09 16:35:26.174|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:35:26.791|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:35:26.866|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:35:26.919|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:35:26.965|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:35:26.987|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 16:35:27.065|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 16:35:27.066|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 16:35:27.074|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 16:35:27.074|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 16:35:27.595|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-08-09 16:35:27.598|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-09 16:35:27.774|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-09 16:35:27.949|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-09 16:35:28.064|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-09 16:35:28.412|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-09 16:35:28.724|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-08-09 16:35:29.131|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-08-09 16:35:29.216|INFO| N/A||main|c.a.c.n.r.NacosServiceRegistry:62|nacos registry, businesses-gateway 198.18.0.1:9000 register finished
2025-08-09 16:35:29.367|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 9.869 seconds (JVM running for 10.989)
2025-08-09 16:35:29.368|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-08-09 16:35:29.368|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-08-09 16:35:29.372|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-08-09 16:35:29.431|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-08-09 16:37:26.400|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 16:38:51.045|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:74|De-registering from Nacos Server now...
2025-08-09 16:38:51.051|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:93|De-registration finished.
2025-08-09 17:03:01.044|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-08-09 17:03:01.071|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-qingsong with PID 9696 (D:\work\projects\businesses-gateway\target\classes started by LENOVO in D:\work\projects\businesses-gateway)
2025-08-09 17:03:01.072|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: dev
2025-08-09 17:03:02.198|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-09 17:03:02.202|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-09 17:03:02.234|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-09 17:03:02.388|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-08-09 17:03:02.391|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-08-09 17:03:02.543|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=09842346-7c05-313c-8e8f-1914436bc553
2025-08-09 17:03:02.610|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://**********:9100!
2025-08-09 17:03:02.613|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://**********:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-08-09 17:03:03.197|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:03:03.202|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:03:03.228|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:03:03.312|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$c421a9a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:03:03.331|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:03:03.332|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:03:03.334|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:03:04.858|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-08-09 17:03:04.859|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-08-09 17:03:04.859|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-08-09 17:03:04.860|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-08-09 17:03:04.860|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-08-09 17:03:04.860|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-08-09 17:03:04.860|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-08-09 17:03:04.861|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-08-09 17:03:04.861|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-08-09 17:03:04.861|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-08-09 17:03:04.861|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-08-09 17:03:04.861|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-08-09 17:03:04.861|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-09 17:03:05.075|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:03:05.356|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:03:05.855|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:03:06.355|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:03:06.356|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties]
2025-08-09 17:03:06.855|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:03:06.856|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: bizconfig, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties].
2025-08-09 17:03:06.856|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 17:03:07.456|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:03:07.522|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:03:07.578|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:03:07.624|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:03:07.649|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:03:07.739|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 17:03:07.739|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 17:03:07.747|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 17:03:07.747|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 17:03:08.255|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-08-09 17:03:08.258|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-09 17:03:08.424|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-09 17:03:08.583|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-09 17:03:08.690|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-09 17:03:09.049|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-09 17:03:09.358|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-08-09 17:03:09.759|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-08-09 17:03:09.844|INFO| N/A||main|c.a.c.n.r.NacosServiceRegistry:62|nacos registry, businesses-gateway 198.18.0.1:9000 register finished
2025-08-09 17:03:09.988|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 10.165 seconds (JVM running for 12.576)
2025-08-09 17:03:09.989|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-08-09 17:03:09.989|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-08-09 17:03:09.993|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-08-09 17:03:10.053|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-08-09 17:05:07.084|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 17:07:07.087|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 17:08:05.380|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:09:07.089|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 17:11:07.091|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 17:12:54.264|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:74|De-registering from Nacos Server now...
2025-08-09 17:12:54.269|INFO| N/A||SpringContextShutdownHook|c.a.c.n.r.NacosServiceRegistry:93|De-registration finished.
2025-08-09 17:13:03.144|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-08-09 17:13:03.175|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-qingsong with PID 19384 (D:\work\projects\businesses-gateway\target\classes started by LENOVO in D:\work\projects\businesses-gateway)
2025-08-09 17:13:03.175|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: dev
2025-08-09 17:13:04.917|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-09 17:13:04.921|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-09 17:13:04.960|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-09 17:13:05.165|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-08-09 17:13:05.168|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-08-09 17:13:05.351|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=09842346-7c05-313c-8e8f-1914436bc553
2025-08-09 17:13:05.439|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://**********:9100!
2025-08-09 17:13:05.445|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://**********:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-08-09 17:13:06.146|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:13:06.153|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:13:06.193|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:13:06.302|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$30615ddf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:13:06.330|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:13:06.332|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:13:06.335|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-09 17:13:08.154|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-08-09 17:13:08.155|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-08-09 17:13:08.155|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-08-09 17:13:08.155|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-08-09 17:13:08.156|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-08-09 17:13:08.156|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-08-09 17:13:08.156|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-08-09 17:13:08.156|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-08-09 17:13:08.156|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-08-09 17:13:08.157|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-08-09 17:13:08.157|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-08-09 17:13:08.157|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-08-09 17:13:08.157|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-09 17:13:08.396|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:13:08.691|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:13:09.194|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:13:09.688|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:13:09.688|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties]
2025-08-09 17:13:10.187|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Load Apollo Config failed - appId: businesses-gateway, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/businesses-gateway/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: businesses-gateway, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
2025-08-09 17:13:10.189|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: bizconfig, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+bizconfig.properties].
2025-08-09 17:13:10.189|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 17:13:10.846|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:13:10.918|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:13:10.974|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:13:11.026|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:13:11.047|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-08-09 17:13:11.122|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 17:13:11.122|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 17:13:11.131|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-08-09 17:13:11.131|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-09 17:13:11.670|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-08-09 17:13:11.672|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-09 17:13:11.816|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-09 17:13:11.973|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-09 17:13:12.078|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-09 17:13:12.439|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-09 17:13:12.722|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-08-09 17:13:13.133|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-08-09 17:13:13.222|INFO| N/A||main|c.a.c.n.r.NacosServiceRegistry:62|nacos registry, businesses-gateway 198.18.0.1:9000 register finished
2025-08-09 17:13:13.369|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 11.402 seconds (JVM running for 12.541)
2025-08-09 17:13:13.370|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-08-09 17:13:13.370|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-08-09 17:13:13.373|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-08-09 17:13:13.437|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-08-09 17:15:10.409|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-08-09 17:17:11.866|WARN| N/A||RefreshConfig-Thread--thread-31|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace bizconfig from Apollo, please check whether the configs are released in Apollo! Return default value now!
