package com.yxt.safecenter.auth.sdk.core.transform.config;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 参数映射配置管理器
 * 负责加载和管理参数转换配置
 *
 * 注意：sys.* 表达式现在从配置文件获取系统参数，而不是从请求头获取
 *
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@Component
public class ParameterMappingConfigManager {

    /**
     * 根据请求路径获取配置
     *
     * @param path 请求路径
     * @return 参数映射配置，如果没有匹配的配置则返回null
     */
    public ParameterMappingConfig getConfig(String path) {
        String mapping = "{\n" +
                "  \"request\": {\n" +
                "    \"mapping\": {\n" +
                "      \"header\": {\n" +
                "        \"headerTest\": \"sys.sysParam\"\n" +
                "      },\n" +
                "      \"query\": {\n" +
                "        \"scene\": \"query.device\"\n" +
                "      },\n" +
                "      \"body\": {\n" +
                "        \"sys\": \"sys.systemId\",\n" +
                "        \"scene\": \"body.device\",\n" +
                "        \"bizCode\": \"const.固定编码示例\",\n" +
                "        \"userId\": \"header.userId\",\n" +
                "        \"diseaseDictId\": \"${body.dId ? 1 : 0}\",\n" +
                "        \"requestId\": \"${defaultEmpty(body.requestId, header.user-key)}\"\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"response\": {\n" +
                "    \"mapping\": {\n" +
                "      \"subMsg\": \"msg\",\n" +
                "      \"newData\": \"data\",\n" +
                "      \"data.bizID\": \"data.id\",\n" +
                "      \"data.bizConst\": \"const.12090\",\n" +
                "      \"data.sysTest\": \"sys.sysParam\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        return JSON.parseObject(mapping, ParameterMappingConfig.class);
    }

    /**
     * 检查转换功能是否启用
     */
    public boolean isTransformEnabled() {
        return true;
    }
}
