package com.yxt.safecenter.auth.sdk.core.transform.core;

import com.yxt.safecenter.auth.sdk.core.transform.config.ParameterMappingConfig;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 参数转换器接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
public interface ParameterTransformer {
    
    /**
     * 转换请求参数
     * 
     * @param exchange 服务器交换对象
     * @param config 映射配置
     * @return 转换后的交换对象
     */
    Mono<ServerWebExchange> transformRequest(ServerWebExchange exchange, ParameterMappingConfig config);
    
    /**
     * 转换响应参数
     * 
     * @param originalResponse 原始响应体
     * @param config 映射配置
     * @param exchange 服务器交换对象
     * @return 转换后的响应体
     */
    Mono<String> transformResponse(String originalResponse, ParameterMappingConfig config, ServerWebExchange exchange);
}
