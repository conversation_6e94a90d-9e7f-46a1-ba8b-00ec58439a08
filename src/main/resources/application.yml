uploadRequestBodySizeDataOnOff: true
route-forbidden-config:
  urlList:
    - /actuator/shutdown
# hyDeeRequestDecoratorReleaseOnOff: true
alarm:
  sendErrorLog:
    # 非必填，接收人员ids（获取方式请看下图），单个示例：1152993；多个示例：1152993|1152694，目前已经根据分组发送，新项目才需要配置，多个使用|分隔
    touser: 1151841|1152855|1153741|1152701|1156046|1155138|1152993|1154440|1154982|1153550|1155137|1153775|1154247|1153742|1152700|1152702|1153777|1152694|1152848|1147311|1153123|1152698|1151551
    sendErrorLog: false
# logging:
#   config: classpath:logback-spring.xml

server:
  port: 9000
spring:
  codec:
    max-in-memory-size: 512KB
  application:
    name: businesses-gateway
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
  cloud:
    discovery:
      client:
        health-indicator:
          enabled: false
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848;
        namespace: fca9f3f3-c2a7-49ad-a3ea-d5fe988ceb30
        metadata:
          department: NR
    sentinel:
      filter:
        enabled: false
      scg:
        fallback:
          enabled: true
          ## response返回文字提示信息，
          mode: response
          response-status: 200
          response-body: '{"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}'
          content-type: application/json
      transport:
        dashboard: dev-sentinel.hxyxt.com
      eager: true
      data-source:
        ## 配置流控规则，名字任意
        flow:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-gateway-flow-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-flow
        ## 配置流控规则，名字任意
        api:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-gateway-api-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-api-group
        ## 配置降级规则，名字任意
        degrade:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-degrade-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: degrade
    inetutils:
      preferred-networks:
        - ^10\.200.+
    gateway:
      httpclient:
        connect-timeout: 2000
        response-timeout: 20s
        pool:
          max-idle-time: PT10S
          eviction-interval: PT30S
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
      routes:
        # develop-assistant
        - id: devops-assistant
          uri: lb://devops-assistant-chat
          predicates:
            - Path=/develop-assistant/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
          # 过滤器配置
          metadata:
            response-timeout: 60000  # 响应超时时间（毫秒）
            connect-timeout: 2000   # 连接超时时间（毫秒）
        # 支付中台
        - id: yxt-payment
          uri: lb://yxt-payment
          predicates:
            - Path=/yxt-payment/**
          filters:
            - StripPrefix=1
        # 慢病中台
        - id: yxt-middle-chronic
          uri: lb://yxt-middle-chronic
          predicates:
            - Path=/chronic/**
          filters:
            - StripPrefix=1
        # 回访中台
        - id: yxt-middle-visit
          uri: lb://yxt-middle-visit
          predicates:
            - Path=/visit/**
          filters:
            - StripPrefix=1
        # 一心助手中台服务跳转任务
        - id: assist-middle-portal-task
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/c/article/**,/assist-middle-portal/c/formEvent/**,/assist-middle-portal/c/stats/**,/assist-middle-portal/c/task/**,/assist-middle-portal/c/taskItem/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 积分
        - id: yxt-middle-integral
          uri: lb://yxt-middle-integral
          predicates:
            - Path=/integral/**
          filters:
            - StripPrefix=1
        # 商户中台
        - id: hydee-middle-baseinfo
          uri: lb://hydee-middle-baseinfo
          predicates:
            - Path=/baseinfo/**
          filters:
            - StripPrefix=1
        # 商户平台
        - id: ydjia-merchant-platform
          uri: lb://ydjia-merchant-platform
          #uri: http://127.0.0.1:10004
          predicates:
            - Path=/merchant/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        - id: ydjia-operate
          uri: lb://ydjia-operate
          predicates:
            - Path=/operate/**
          filters:
            - StripPrefix=1
        # 药店加小前台
        - id: ydjia-merchant-manager
          uri: lb://ydjia-merchant-manager
          predicates:
            - Path=/mer-manager/**
          filters:
            - StripPrefix=1
        # 电商云O2O
        - id: hydee-business-order
          uri: lb://hydee-business-order
          predicates:
            - Path=/dscloud/**
          filters:
            - StripPrefix=1
        # 电商云B2C
        - id: hydee-business-order-web
          uri: lb://hydee-business-order-web
          predicates:
            - Path=/b2c/**
          filters:
            - StripPrefix=1
        # 会员中台
        - id: hydee-middle-member
          uri: lb://hydee-middle-member
          predicates:
            - Path=/member/**
          filters:
            - StripPrefix=1
        # 商品中台
        - id: hydee-middle-merchandise
          uri: lb://hydee-middle-merchandise
          predicates:
            - Path=/merchandise/**
          filters:
            - StripPrefix=1
            - SafeCenterAuth
        # 商品搜索
        - id: yxt-merchandise-search
          uri: lb://yxt-merchandise-search
          predicates:
            - Path=/merchandiseSearch/**
          filters:
            - StripPrefix=1
        # 微商城商品服务
        - id: hydee-middle-goods
          uri: lb://hydee-middle-goods
          predicates:
            - Path=/goods/**
          filters:
            - StripPrefix=1
        # 促销小前台
        - id: ydjia-merchant-promote
          uri: lb://ydjia-merchant-promote
          predicates:
            - Path=/promote/**
          filters:
            - StripPrefix=1
        # C端小前台
        - id: ydjia-merchant-customer
          uri: lb://ydjia-merchant-customer
          predicates:
            - Path=/customer/**
          filters:
            - StripPrefix=1
        # 支付中台
        - id: hydee-middle-payment
          uri: lb://hydee-middle-payment
          predicates:
            - Path=/payment/**
          filters:
            - StripPrefix=1
        # 小密桥数据
        - id: honey-bridge-data
          uri: lb://honey-bridge-data
          predicates:
            - Path=/honey-bridge-data/**
          filters:
            - StripPrefix=1
        # 小密医疗
        - id: honey-medical
          uri: lb://honey-medical
          predicates:
            - Path=/honey-medical/**
          filters:
            - StripPrefix=1
        # 小密ERP服务
        - id: honey-erp-service
          uri: lb://honey-erp-service
          predicates:
            - Path=/honey-erp-service/**
          filters:
            - StripPrefix=1
        # 小密activity
        - id: honey-activity
          uri: lb://honey-activity
          predicates:
            - Path=/honey-activity/**
          filters:
            - StripPrefix=1
        # 小密推送服务
        - id: hydee-middle-push
          uri: lb://hydee-middle-push
          predicates:
            - Path=/hydee-middle-push/**
          filters:
            - StripPrefix=1
        # 药店加统计服务
        - id: ydjia-statistic
          uri: lb://ydjia-statistic
          predicates:
            - Path=/ydjia-statistic/**
          filters:
            - StripPrefix=1
        # 口罩预约
        - id: mask-server
          uri: lb://mask-server
          predicates:
            - Path=/mask/**
          filters:
            - StripPrefix=1
        # 迁移服务
        - id: migrate
          uri: lb://migrate
          predicates:
            - Path=/migrate/**
          filters:
            - StripPrefix=1
          # 企业微信服务
        - id: hydee-ewx-service
          uri: lb://hydee-ewx-service
          predicates:
            - Path=/hydee-ewx-service/**
          filters:
            - StripPrefix=1
        # 企业微信小蜜服务
        - id: hydee-ewx-honey
          uri: lb://hydee-ewx-honey
          predicates:
            - Path=/hydee-ewx-honey/**
          filters:
            - StripPrefix=1
        - id: hydee-ewx-honey-py
          uri: lb://hydee-ewx-honey-py
          predicates:
            - Path=/hydee-ewx-honey-py/**
          filters:
            - StripPrefix=1
        # 微信直播
        - id: hydee-live-service
          uri: lb://hydee-live-service
          predicates:
            - Path=/hydee-live-service/**
          filters:
            - StripPrefix=1
        # 会员中台
        - id: hydee-middle-member
          uri: lb://hydee-middle-member
          predicates:
            - Path=/member/**
          filters:
            - StripPrefix=1
        # 微商城OMS对接服务
        - id: middle-datasync-message
          uri: lb://middle-datasync-message
          predicates:
            - Path=/datasync/**
          filters:
            - StripPrefix=1
        # 库存全量同步走定向服务器
        - id: merchandise-sync
          uri: lb://hydee-middle-merchandise-sync
          predicates:
            - Path=/merchandise/1.0/sync/**
          filters:
            - StripPrefix=1
        # ERP数据同步
        - id: hydee-middle-syncerp
          uri: lb://hydee-middle-syncerp
          predicates:
            - Path=/syncerp/**
          filters:
            - StripPrefix=1
        # 服务商小前台
        - id: hydee-sp-platform
          uri: lb://hydee-sp-platform
          predicates:
            - Path=/sp-platform/**
          filters:
            - StripPrefix=1
        # 报表服务
        - id: ydjia-report
          uri: lb://ydjia-report
          predicates:
            - Path=/ydjia-report/**
          filters:
            - StripPrefix=1
        # 供应商商品模块
        - id: ydjia-srm-goods
          uri: lb://ydjia-srm-goods
          predicates:
            - Path=/srm-goods/**
          filters:
            - StripPrefix=1
        # 供应商发货模块
        - id: ydjia-srm-delivery
          uri: lb://ydjia-srm-delivery
          predicates:
            - Path=/srm-delivery/**
          filters:
            - StripPrefix=1
        # 供应商账单模块
        - id: ydjia-srm-bills
          uri: lb://ydjia-srm-bills
          predicates:
            - Path=/srm-bills/**
          filters:
            - StripPrefix=1
        # 商户服务定制化模块
        - id: ydjia-adaptation
          uri: lb://ydjia-adaptation
          predicates:
            - Path=/ydjia-adaptation/**
          filters:
            - StripPrefix=1
        # 商品中台数据处理(erp/三方数据)
        - id: hydee-merchandise-data-processor
          uri: lb://hydee-merchandise-data-processor
          predicates:
            - Path=/data-processor/**
          filters:
            - StripPrefix=1
        # 预警中台
        - id: hydee-middle-alerting
          uri: lb://hydee-middle-alerting
          predicates:
            - Path=/hydee-middle-alerting/**
          filters:
            - StripPrefix=1
        # market服务
        - id: hydee-middle-market
          uri: lb://hydee-middle-market
          predicates:
            - Path=/market/**
          filters:
            - StripPrefix=1
        # promotion服务
        - id: yxt-middle-promotion
          uri: lb://yxt-middle-promotion
          predicates:
            - Path=/promotion/**
          filters:
            - StripPrefix=1
        # message-center服务
        - id: yxt-middle-message-center
          uri: lb://yxt-middle-message-center
          predicates:
            - Path=/message-center/**
          filters:
            - StripPrefix=1
        # 账户中心
        - id: hydee-middle-account-center
          uri: lb://hydee-middle-account-center
          predicates:
            - Path=/account-center/**
          filters:
            - StripPrefix=1
        # 同步服务
        - id: hydee-middle-data-sync
          uri: lb://hydee-middle-data-sync
          predicates:
            - Path=/data-sync/**
          filters:
            - StripPrefix=1
            - SafeCenterAuth
        # 随心看
        - id: hydee-data-center-follow-heart
          uri: lb://hydee-data-center-business
          predicates:
            - Path=/hydee-data-center-business/**
          filters:
            - StripPrefix=1
        # 随心看
        - id: hydee-middle-third
          uri: lb://hydee-middle-third
          predicates:
            - Path=/middle-third/**
          filters:
            - StripPrefix=1
        # 一心助手任务中台
        - id: assist-task
          uri: lb://assist-task
          predicates:
            - Path=/assist-task/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手任务中台
        - id: assist-task-local
          uri: lb://assist-task-local
          predicates:
            - Path=/assist-task-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手任务中台
        - id: assist-task-local-zm
          uri: lb://assist-task-local-zm
          predicates:
            - Path=/assist-task-local-zm/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手统计
        - id: yxt-form
          uri: lb://yxt-form
          predicates:
            - Path=/yxt-form/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手动态表单本地
        - id: yxt-form-local
          uri: lb://yxt-form-local
          predicates:
            - Path=/yxt-form-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手资源服务
        - id: assist-resource
          uri: lb://assist-resource
          predicates:
            - Path=/assist-resource/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手成长中心
        - id: assist-growth
          uri: lb://assist-growth
          predicates:
            - Path=/assist-growth/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手成长中心
        - id: assist-growth-local
          uri: lb://assist-growth-local
          predicates:
            - Path=/assist-growth-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手统计
        - id: assist-stats-local
          uri: lb://assist-stats-local
          predicates:
            - Path=/assist-stats-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess

        # 前台消息
        - id: assist-middle-portal-message
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/c/message/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 前台用户设备信息
        - id: assist-middle-portal-message
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/c/userdevice/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手中台门面
        - id: assist-middle-portal
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手中台门面
        - id: assist-middle-portal-local
          uri: lb://assist-middle-portal-local
          predicates:
            - Path=/assist-middle-portal-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手中台门面
        - id: assist-middle-portal-local-zm
          uri: lb://assist-middle-portal-local-zm
          predicates:
            - Path=/assist-middle-portal-local-zm/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手中台门面
        - id: assist-middle-portal-local-lj
          uri: lb://assist-middle-portal-local-lj
          predicates:
            - Path=/assist-middle-portal-local-lj/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手中台门面
        - id: assist-middle-portal-local-wrh
          uri: lb://assist-middle-portal-local-wrh
          predicates:
            - Path=/assist-middle-portal-local-wrh/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手统计
        - id: assist-stats
          uri: lb://assist-stats
          predicates:
            - Path=/assist-stats/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手统计
        - id: assist-stats-wrh
          uri: lb://assist-stats-wrh
          predicates:
            - Path=/assist-stats-wrh/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手统计
        - id: assist-stats-local-lf
          uri: lb://assist-stats-local-lf
          predicates:
            - Path=/assist-stats-local-lf/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手首页应用
        - id: assist
          uri: lb://assist-home
          predicates:
            - Path=/assist-home/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
          # 过滤器配置
          metadata:
            response-timeout: 10000  # 响应超时时间（毫秒）
            connect-timeout: 2000   # 连接超时时间（毫秒）
        # 一心助手首页应用
        - id: assist-home-local
          uri: lb://assist-home-local
          predicates:
            - Path=/assist-home-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手首页应用
        - id: assist-home-local-lf
          uri: lb://assist-home-local-lf
          predicates:
            - Path=/assist-home-local-lf/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手首页应用
        - id: assist-home-local-cl
          uri: lb://assist-home-local-cl
          predicates:
            - Path=/assist-home-local-cl/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手首页应用
        - id: assist-home-local-lzl
          uri: lb://assist-home-local-lzl
          predicates:
            - Path=/assist-home-local-lzl/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务-门店申请相关
        - id: assist-synthesis-store-order
          uri: lb://assist-core-toolkit
          predicates:
            - Path=/assist-synthesis/c/storeOrder/**,/assist-synthesis/c/storeOrderBizConfig/**,/assist-synthesis/c/storeReplenish/**,/assist-synthesis/c/bizCommodity/**,/assist-synthesis/b/storeOrder/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务
        - id: assist-synthesis
          uri: lb://assist-synthesis
          predicates:
            - Path=/assist-synthesis/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手·工具服务
        - id: assist-core-toolkit
          uri: http://dev-api.hxyxt.com
          predicates:
            - Path=/assist-core-toolkit/**
          filters:
            - RewritePath=^(.*)$, /poseidon/$1 # 正则匹配并重写路径

        # 一心助手工具服务2
        - id:  assist-core-toolkit-lij
          uri: lb://assist-core-toolkit-lij
          predicates:
            - Path=/assist-core-toolkit-lij/**
          filters:
            - StripPrefix=1
        - id: assist-core-toolkit-yjh
          uri: lb://assist-core-toolkit-yjh
          predicates:
            - Path=/assist-core-toolkit-yjh/**
          filters:
            - StripPrefix=1
        # 一心助手综合服务
        - id: assist-synthesis-local
          uri: lb://assist-synthesis-local
          predicates:
            - Path=/assist-synthesis-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务
        - id: assist-synthesis-local-cl
          uri: lb://assist-synthesis-local-cl
          predicates:
            - Path=/assist-synthesis-local-cl/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务
        - id: assist-synthesis-local-lf
          uri: lb://assist-synthesis-local-lf
          predicates:
            - Path=/assist-synthesis-local-lf/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务
        - id: assist-synthesis-local-lzl
          uri: lb://assist-synthesis-local-lzl
          predicates:
            - Path=/assist-synthesis-local-lzl/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务
        - id: assist-synthesis-common
          uri: lb://assist-synthesis-common
          predicates:
            - Path=/assist-synthesis-common/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手小前台
        - id: assist-prospect
          uri: lb://assist-prospect
          predicates:
            - Path=/assist-prospect/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助basis
        - id: assist-basis-wum
          uri: lb://assist-basis-wum
          predicates:
            - Path=/assist-basis-wum/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手小前台local
        - id: assist-prospect-local
          uri: lb://assist-prospect-local
          predicates:
            - Path=/assist-prospect-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手小前台local-zm
        - id: assist-prospect-local-zm
          uri: lb://assist-prospect-local-zm
          predicates:
            - Path=/assist-prospect-local-zm/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 流程引擎
        - id: yxt-workflow
          uri: lb://yxt-workflow
          predicates:
            - Path=/yxt-workflow/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        - id: yxt-login
          uri: lb://yxt-login
          predicates:
            - Path=/yxt-login/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        - id: yxt-login-local
          uri: lb://yxt-login-local
          predicates:
            - Path=/yxt-login-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 流程引擎
        - id: yxt-workflow-local
          uri: lb://yxt-workflow-local
          predicates:
            - Path=/yxt-workflow-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 流程引擎
        - id: yxt-workflow-local-wum
          uri: lb://yxt-workflow-local-wum
          predicates:
            - Path=/yxt-workflow-local-wum/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手论坛
        - id: assist-forum
          uri: lb://assist-forum
          predicates:
            - Path=/assist-forum/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手论坛
        - id: assist-forum-local-wrh
          uri: lb://assist-forum-local-wrh
          predicates:
            - Path=/assist-forum-local-wrh/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        #临时测试支付接口
        - id: h3-pay-core
          uri: lb://h3-pay-core
          predicates:
            - Path=/paycore/**
          filters:
            - StripPrefix=1
        #APP消息平台
        - id: yxt-app-push
          uri: lb://yxt-app-push
          predicates:
            - Path=/yxt-app-push/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        #公告导出服务
        - id: yxt-export
          uri: lb://yxt-export
          predicates:
            - Path=/yxt-export/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        #公告导出服务
        - id: yxt-export-local
          uri: lb://yxt-export-local
          predicates:
            - Path=/yxt-export-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手成长中心
        - id: assist-growth-cl
          uri: lb://assist-growth-cl
          predicates:
            - Path=/assist-growth-cl/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 大数据心镜
        - id: yxt-bigdata-decision
          uri: lb://yxt-bigdata-decision
          predicates:
            - Path=/decision/**
          filters:
            - StripPrefix=1
          metadata: # 路由级别的超时配置，优先级大于网关的全局超时配置
            connect-timeout: 60000
            response-timeout: 60000
        # 一心基础组件服务
        - id: yxt-basis
          uri: lb://yxt-basis
          predicates:
            - Path=/yxt-basis/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心基础组件服务
        - id: yxt-basis-local
          uri: lb://yxt-basis-local
          predicates:
            - Path=/yxt-basis-local/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心基础组件服务
        - id: yxt-basis-cl
          uri: lb://yxt-basis-cl
          predicates:
            - Path=/yxt-basis-cl/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心基础组件服务
        - id: yxt-basis-local-lj
          uri: lb://yxt-basis-local-lj
          predicates:
            - Path=/yxt-basis-local-lj/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心基础组件服务
        - id: assist-basis
          uri: lb://assist-basis
          predicates:
            - Path=/assist-basis/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 大数据心智
        - id: yxt-bigdata-mind
          uri: lb://yxt-bigdata-mind
          predicates:
            - Path=/mind/**
          filters:
            - StripPrefix=1
        # 会员中台
        - id: hydee-middle-member-local
          uri: lb://hydee-middle-member-local
          predicates:
            - Path=/member-local/**
          filters:
            - StripPrefix=1
        # 数璟
        - id: data-fusion-webfull
          uri: lb://data-fusion-webfull
          predicates:
            - Path=/data-fusion-webfull/**
          filters:
            - StripPrefix=1
        # 数璟
        - id: data-fusion-webfull-local
          uri: lb://data-fusion-webfull-local
          predicates:
            - Path=/data-fusion-webfull-local/**
          filters:
            - StripPrefix=1
        # 数璟respond
        - id: data-fusion-respond
          uri: lb://data-fusion-respond
          predicates:
            - Path=/data-fusion-respond/**
          filters:
            - StripPrefix=1
        - id: yxt-medical-prescription
          uri: lb://yxt-medical-prescription
          predicates:
            - Path=/yxt-medical-prescription/**
          filters:
            - StripPrefix=1
        # 大数据ai
        - id: yxt-bigdata-ai
          uri: lb://yxt-bigdata-ai
          predicates:
            - Path=/bigdataai/**
          filters:
            - StripPrefix=1
        # 大数据bee
        - id: yxt-bigdata-bee
          uri: lb://yxt-bigdata-bee
          predicates:
            - Path=/bigdata-bee/**
          filters:
            - StripPrefix=1
        # 大数据cicada
        - id: yxt-bigdata-cicada
          uri: lb://yxt-bigdata-cicada
          predicates:
            - Path=/bigdata-cicada/**
          filters:
            - StripPrefix=1
        # 大数据alarm
        - id: yxt-bigdata-alarm
          uri: lb://yxt-bigdata-alarm
          predicates:
            - Path=/bigdata-alarm/**
          filters:
            - StripPrefix=1
        # 大数据bear
        - id: yxt-bigdata-bear
          uri: lb://yxt-bigdata-bear
          predicates:
            - Path=/bigdata-bear/**
          filters:
            - StripPrefix=1
        # 一心助手HCM
        - id: assist-hcm
          uri: lb://assist-hcm
          predicates:
            - Path=/assist-hcm/**
          filters:
            - StripPrefix=1
            # ==走新网关认证===
            - YxtLoginAccess

        # 一心堂小前台
        - id: yxt-org-aspect
          uri: lb://yxt-basis-prospect
          predicates:
            - Path=/yxt-org-aspect/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手B2B加盟商城
        - id: yxt-mall-b2b
          uri: lb://yxt-mall-b2b
          predicates:
            - Path=/yxt-mall-b2b/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心堂安全中心
        - id: yxt-safe-center
          uri: lb://yxt-safe-center
          predicates:
            - Path=/safe-center/**
          filters:
            - StripPrefix=1
            - SafeCenterAuth
            - YxtLoginAccess
        # 客服中台
        - id: customer-service-center
          uri: lb://customer-service-center
          predicates:
            - Path=/customer-service/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 订单新模型
        - id: order-service
          uri: lb://order-service
          predicates:
            - Path=/order-world/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心堂基础前台服务
        - id: yxt-basis-prospect
          uri: lb://yxt-basis-prospect
          predicates:
            - Path=/yxt-basis-prospect/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心堂基础前台服务
        - id: yxt-basis-prospect-lj
          uri: lb://yxt-basis-prospect-lj
          predicates:
            - Path=/yxt-basis-prospect-lj/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 评价中台
        - id: evaluation-center
          uri: lb://evaluation-center
          predicates:
            - Path=/evaluation-center/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 会员、慢病数据开发服务
        - id: yxt-bigdata-deer
          uri: lb://yxt-bigdata-deer
          predicates:
            - Path=/deer/**
          filters:
            - StripPrefix=1
          metadata: # 路由级别的超时配置，优先级大于网关的全局超时配置
            connect-timeout: 60000
            response-timeout: 60000
        # 大数据自助取数
        - id: yxt-bigdata-rabbit
          uri: lb://yxt-bigdata-rabbit
          predicates:
            - Path=/bigdata-rabbit/**
          filters:
            - StripPrefix=1
        # 大数据自助取数
        - id: yxt-bigdata-hare
          uri: lb://yxt-bigdata-hare
          predicates:
            - Path=/bigdata-hare/**
          filters:
            - StripPrefix=1
        # 大数据新智能请货
        - id: yxt-bigdata-turtle
          uri: lb://yxt-bigdata-turtle
          predicates:
            - Path=/bigdata-turtle/**
          filters:
            - StripPrefix=1
        # 价格中台
        - id: yxt-lot-price
          uri: lb://yxt-lot-price
          predicates:
            - Path=/lot-price/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 采购中台
        - id: purchase-order-center
          uri: lb://purchase-order-center
          predicates:
            - Path=/purchase-center/**
          filters:
            - StripPrefix=1
        # sale-task
        - id: yxt-middle-sale-task
          uri: lb://yxt-middle-sale-task
          predicates:
            - Path=/sale-task/**
          filters:
            - StripPrefix=1
        #门店店铺系统
        - id: yxt-store-base
          uri: lb://yxt-store-base
          predicates:
            - Path=/yxt-store-base/**
          filters:
            - StripPrefix=1
        
        # MCP配置中台-local
        - id: yxt-mcp-manager-server-local
          uri: lb://yxt-mcp-manager-server-local
          predicates:
            - Path=/yxt-mcp-manager-server-local/**
          filters:
            - StripPrefix=1
        # MCP配置中台
        - id: yxt-mcp-manager-server
          uri: lb://yxt-mcp-manager-server
          predicates:
            - Path=/yxt-mcp-manager-server/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
  redis:
    password: yxt_redis123
    jedis:
      pool:
        min-idle: 10
        max-active: 200
        max-idle: 50
        max-wait: 1000
    timeout: 1000
    cluster:
      nodes: **********:9000,**********:9001,**********:9002,**********:9003,**********:9004,**********:9005
      max-redirects: 3
api:
  base-info-version: 1.0
  version: 1.0

management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
    metrics:
      enabled: true
    gateway:
      enabled: true
    health:
      enabled: true
      show-details: always
  health:
    defaults:
      enabled: false
  endpoints:
    web:
      exposure:
        include: [ "*" ]


rest:
  connectTimeout: 3000
  readTimeout: 6000
jwt:
  expire: 14400
  rsa-secret: xx1WET12^%3^(WE45
auth:
  user:
    token-header: Authorization

idempotent:
  expire-mill: 2000
  post-path:
    - /merchant/1.0/org
    - /merchant/1.0/authority
    - /merchant/1.0/employee
    - /operate/1.0/package
    - /operate/1.0/merchant
    - /operate/1.0/merchant/_purchasePkg
    - /operate/1.0/authority
    - /operate/1.0/authority/_createAcc
    - /mer-manager/1.0/pageset
    - /mer-manager/1.0/commodity
    - /mer-manager/1.0/commodity/self
    - /mer-manager/1.0/comm-dimen/add
    - /mer-manager/1.0/comm-type/addType
    - /mer-manager/1.0/assemble-comm
    - /promote/1.0/admin/activities
    - /mer-manager/1.0/csd-msg
    - /mask/1.0/b/store/_save
    - /mask/1.0/b/product/_save
gate:
  ignore:
    start-with:
      - /datasync/
      - /promote/
      - /open
      - /customer/
      - /ydjia-statistic/
      - /payment/1.0/receive/notify
      - /payment/1.0/receive/return
      - /payment/payment/payNotify
      - /payment/payment/refundNotify
      - /payment/payment/sharingNotify
      - /payment/1.0/unifiedPay/payNotify
      - /payment/1.0/unifiedPay/refundNotify
      - /payment/account/settleNotify
      - /merchant/1.0/acc/_queryAccountStatus/
      - /merchant/1.0/verification/
      - /merchant/1.0/acc/_activateAccount
      - /merchant/1.0/acc/getSelfBuildAppLoginUrl
      - /merchant/1.0/acc/check_user
      - /merchant/1.0/acc/checkShopStaff
      - /merchant/1.0/acc/update/avatar
      - /merchant/1.0/file/_upload
      - /mer-manager/1.0/express-record
      - /mer-manager/1.0/weeChatOpen/auth
      - /mer-manager/1.0/weeChatOpen/recvMsg/
      - /middle-third/1.0/open/receiveTicket
      - /middle-third/1.0/open/recvMsg/
      - /middle-third/1.0/open/authCallBack
      - /honey-medical/v1/mobileUser/getToken
      - /honey-medical/v1/monitor/healthCheck
      - /honey-medical/v1/scanLogin/getScanElement
      - /honey-medical/v1/scanLogin/isAppLoginCompleted
      - /honey-medical/v1/sso/getSSOToken
      - /merchandise/1.0/store-spec/sync
      - /merchandise/1.0/ds/op/file
      - /merchandise/1.0/sync/stock
      - /merchandise/1.0/sync/price
      - /merchandise/1.0/sync/commAndStore
      - /merchandise/1.0/open
      - /merchandise/1.0/comm-spec/access-plat
      - /mask/1.0/c
      - /dscloud/1.0/ds/print
      - /dscloud/1.0/ds/file
      - /dscloud/1.0/ds/order/callback/orderDelivery
      - /dscloud/1.0/test/graceful
      - /dscloud/1.0/test/timeoutTest
      - /dscloud/1.0/test/timeoutTest2
      - /hydee-ewx-service/1.0/honeyApp/dataCallback
      - /hydee-ewx-service/1.0/honeyApp/orderCallback
      - /hydee-ewx-service/1.0/innerHoneyApp/contactCallback
      - /hydee-ewx-service/1.0/innerHoneyApp/customerCallback
      - /hydee-ewx-service/1.0/customHoneyApp/callback
      - /hydee-ewx-honey/1.0/third/plat/callBack
      - /hydee-ewx-honey/1.0/third/plat/authCallBack
      - /hydee-ewx-honey/1.0/third/plat/recvMsg
      - /hydee-ewx-honey/1.0/ewxService/getMiniInfo
      - /hydee-ewx-honey/1.0/wxclient
      - /hydee-ewx-honey/1.0/boss
      - /hydee-ewx-honey/1.0/login/fromAdmin
      - /hydee-ewx-honey/1.0/login/fromMobile
      - /hydee-ewx-honey/1.0/login/fromInner
      - /hydee-ewx-honey-py/1.0/login/fromAdmin
      - /hydee-ewx-honey-py/1.0/login/fromMobile
      - /hydee-live-service/1.0/videoCallback/recordVideoCallback
      - /hydee-live-service/1.0/videoCallback/pushVideoCallback
      - /member/1.0/memberManage/changeMemberCard
      - /member/1.0/erpMember/incrSyncMember
      - /member/1.0/memberCrowd/getExpertsCrowdInfo
      - /member/1.0/memberCrowd/experts/delete
      - /market/1.0/couponGift/order/callback
      - /mer-manager/1.0/oss/file/upload
      - /mer-manager/1.0/merchantSwitch/batchUpdate
      - /syncerp/1.0.0/pushCompany
      - /syncerp/1.0.0/pushStore
      - /syncerp/1.0.0/pushEmployee
      - /merchant/1.0/cjksso/getToken
      - /merchant/1.0/cjksso/_login
      - /sp-platform/1.0/spAcc/_login
      - /sp-platform/1.0/spAcc/_register
      - /sp-platform/1.0/spAcc/notLoginCheck
      - /sp-platform/1.0/spAcc/_sendCode
      - /sp-platform/1.0/spAcc/_checkVerificationCode
      - /sp-platform/1.0/file/_upload
      - /sp-platform/1.0/spAcc/forgetPassword
      - /ydjia-report/1.0/admin
      - /ydjia-report/vs
      - /ydjia-adaptation
      - /drug/
      - /hydee-middle-alerting/1.0/admin
      - /hydee-middle-alerting/vs
      - /b2c/1.0/oms/order/listByTime
      - /b2c/1.0/oms/order/listRefundByTime
      - /account-center/him/merchant/getList
      - /account-center/him/provider/getList
      - /market/1.0/ispActivity4Him/getDataByMerchant
      - /market/1.0/ispActivity4Him/getDataByIsp
      - /merchant/1.0/acc/_forgetPassword
      - /sp-platform/1.0/spAcc/search/
      - /merchant/1.0/acc/account/query/
      - /assist-synthesis/swagger
      - /assist-synthesis/v2/api-docs
      - /assist-synthesis/webjars
      - /assist-synthesis/swagger-resources
      - /assist-synthesis/doc.html
      - /assist-synthesis/druid
      - /assist-prospect/swagger
      - /assist-prospect/v2/api-docs
      - /assist-prospect/webjars
      - /assist-prospect/swagger-resources
      - /assist-prospect/doc.html
      - /assist-prospect/druid
      - /assist-growth/swagger
      - /assist-growth/v2/api-docs
      - /assist-growth/webjars
      - /assist-growth/swagger-resources
      - /assist-growth/druid
      - /assist-home/swagger
      - /assist-home/druid
      - /assist-home/v2/api-docs
      - /assist-home/webjars
      - /assist-home/swagger-resources
      - /assist-home/doc.html
      - /assist-task/swagger
      - /assist-task/v2/api-docs
      - /assist-task/webjars
      - /assist-task/swagger-resources
      - /assist-task/doc.html
      - /assist-task/druid
      - /assist-middle-portal/swagger
      - /assist-middle-portal/v2/api-docs
      - /assist-middle-portal/webjars
      - /assist-middle-portal/swagger-resources
      - /assist-middle-portal/doc.html
      - /assist-middle-portal/druid
      - /assist-middle-portal/c/common/weChat/r/1.0/getJsSdkAuthConfig
      - /assist-middle-portal/c/material/w/1.0/doSaveMaterialVisit
      - /yxt-basis/swagger
      - /yxt-basis/v2/api-docs
      - /yxt-basis/webjars
      - /yxt-basis/swagger-resources
      - /yxt-basis/doc.html
      - /yxt-basis/druid
      - /yxt-export/swagger
      - /yxt-export/v2/api-docs
      - /yxt-export/webjars
      - /yxt-export/druid
      - /yxt-export/swagger-resources
      - /yxt-export/doc.html
      - /yxt-app-push/swagger
      - /yxt-app-push/v2/api-docs
      - /yxt-app-push/webjars
      - /yxt-app-push/swagger-resources
      - /yxt-app-push/druid
      - /yxt-workflow/swagger
      - /yxt-workflow/v2/api-docs
      - /yxt-workflow/webjars
      - /yxt-workflow/swagger-resources
      - /yxt-workflow/druid
      - /yxt-login/swagger
      - /yxt-login/v2/api-docs
      - /yxt-login/webjars
      - /yxt-login/swagger-resources
      - /yxt-login/druid
      - /yxt-login/doc.html
      - /yxt-workflow-local/swagger
      - /yxt-workflow-local/v2/api-docs
      - /yxt-workflow-local/webjars
      - /yxt-workflow-local/swagger-resources
      - /yxt-workflow-local/druid
      - /yxt-workflow-local-wum/swagger
      - /yxt-workflow-local-wum/v2/api-docs
      - /yxt-workflow-local-wum/webjars
      - /yxt-workflow-local-wum/swagger-resources
      - /yxt-workflow-local-wum/druid
      - /assist-stats-local/swagger
      - /assist-stats-local/v2/api-docs
      - /assist-stats-local/webjars
      - /assist-stats-local/swagger-resources
      - /assist-stats-local/doc.html
      - /assist-stats-local/druid
      - /assist-stats/swagger
      - /assist-stats/v2/api-docs
      - /assist-stats/webjars
      - /assist-stats/swagger-resources
      - /assist-stats/doc.html
      - /assist-stats/druid
      - /assist-basis-wum/swagger
      - /assist-basis-wum/v2/api-docs
      - /assist-basis-wum/webjars
      - /assist-basis-wum/doc.html
      - /assist-basis-wum/druid
      - /yxt-workflow/swagger
      - /yxt-workflow/v2/api-docs
      - /yxt-workflow/webjars
      - /yxt-workflow/doc.html
      - /yxt-workflow/druid
      - /yxt-workflow-local/swagger
      - /yxt-workflow-local/v2/api-docs
      - /yxt-workflow-local/webjars
      - /yxt-workflow-local/doc.html
      - /yxt-workflow-local/druid
      - /yxt-workflow-local-wum/swagger
      - /yxt-workflow-local-wum/v2/api-docs
      - /yxt-workflow-local-wum/webjars
      - /yxt-workflow-local-wum/doc.html
      - /yxt-workflow-local-wum/druid
      - /assist-resource/swagger
      - /assist-resource/v2/api-docs
      - /assist-resource/webjars
      - /assist-resource/doc.html
      - /assist-resource/druid
      - /assist-synthesis/b/common/disposition/r/1.0/listPolling
      - /assist-synthesis/c/common/disposition/r/1.0/listPolling
      - /assist-synthesis/c/appVersion/r/1.0/queryLatestVersion
      - /assist-synthesis/b/appVersion/w/1.0/createAppVersionBySign
      - /decision/swagger
      - /decision/v2/api-docs
      - /decision/webjars
      - /decision/swagger-resources
      - /decision/doc.html
      - /mind/swagger
      - /mind/v2/api-docs
      - /mind/webjars
      - /mind/swagger-resources
      - /mind/doc.html
      - /assist-prospect/common/auth/r/1.0/checkAuthBeforeLogin
      - /assist-home/c/common/front/r/1.0/checkAuthBeforeLogin
      - /assist-home/c/common/front/r/2.0/checkAuthBeforeLogin
      - /assist-home-local-lf/c/common/front/r/2.0/checkAuthBeforeLogin
      - /assist-home/c/common/front/r/2.0/checkAuthBeforeLogin
      - /data-fusion-respond/api/open-api
      - /data-fusion-webfull/api
      - /yxt-medical-prescription/api/1.0/medical/staff/checkUploadSign
      - /yxt-medical-prescription/api/1.0/medical/staff/uploadSign
      - /yxt-medical-prescription/api/1.0/medical/test/chat
      - /bigdataai/bigdata/chart/r/1.0/qw/bot
      - /assist-synthesis/b/appVersion/w/1.0/addAppVersionInfoAndResource
      - /assist-synthesis/b/appVersion/r/1.0/getLatestAppVersionResource
      - /assist-synthesis/b/appVersion/r/1.0/getLatestInstallPackage
      - /assist-synthesis/c/appVersion/r/1.0/getUpdateResource
      - /bigdata-alarm/data/check/r/1.0/es/cargo/push
      - /bigdata-alarm/data/check/r/1.0/data/error/phone
      - /bigdata-alarm/data/check/r/1.0/data/receive/phone
      - /mer-manager/1.0/huawei/voice/callBack
      - /assist-hcm/swagger
      - /assist-hcm/v2/api-docs
      - /assist-hcm/webjars
      - /assist-hcm/swagger-resources
      - /assist-hcm/doc.html
      - /assist-hcm/druid
      - /assist-core-toolkit/swagger
      - /assist-core-toolkit/v2/api-docs
      - /assist-core-toolkit/webjars
      - /assist-core-toolkit/swagger-resources
      - /assist-core-toolkit/doc.html
      - /assist-synthesis/b/appVersion/r/1.0/getAppVersion
      - /assist-synthesis/b/appVersion/r/2.0/getLatestAppVersionResource
      - /mind/commission/msg/r/1.0/qw/receive
      - /yxt-login/c/cas/w/1.0/authentication
      - /yxt-login/outer/cas/r/1.0/authentication
      - /yxt-org-aspect/swagger
      - /yxt-org-aspect/v2/api-docs
      - /yxt-org-aspect/webjars
      - /yxt-org-aspect/swagger-resources
      - /yxt-org-aspect/doc.html
      - /yxt-org-aspect/druid
      - /yxt-login/swagger
      - /yxt-login/v2/api-docs
      - /yxt-login/webjars
      - /yxt-login/swagger-resources
      - /yxt-login/doc.html
      - /yxt-login/druid
      - /yxt-login/b/account/w/1.0/modifyPassword
      - /yxt-mall-b2b/swagger
      - /yxt-mall-b2b/v2/api-docs
      - /yxt-mall-b2b/webjars
      - /yxt-mall-b2b/swagger-resources
      - /yxt-mall-b2b/doc.html
      - /yxt-mall-b2b/druid
      - /yxt-basis-prospect/swagger
      - /yxt-basis-prospect/v2/api-docs
      - /yxt-basis-prospect/webjars
      - /yxt-basis-prospect/swagger-resources
      - /yxt-basis-prospect/druid
      - /yxt-basis-prospect-lj/swagger
      - /yxt-basis-prospect-lj/v2/api-docs
      - /yxt-basis-prospect-lj/webjars
      - /yxt-basis-prospect-lj/swagger-resources
      - /yxt-basis-prospect-lj/druid
      - /yxt-medical-prescription/third/1.0/lianOu/inquiryCallback
      - /assist-middle-portal/c/userdevice/w/1.0/submitAppHeartbeat
      - /assist-middle-portal/c/common/config/r/1.0/listGatewayRouteRule
      - /yxt-mcp-manager-server-local/doc.html
      - /yxt-mcp-manager-server-local/swagger
      - /yxt-mcp-manager-server-local/v3/api-docs
      - /yxt-mcp-manager-server-local/webjars
      - /yxt-mcp-manager-server-local/swagger-resources
      - /yxt-mcp-manager-server-local/druid
    end-with:
      - /_login
      - /login
      - /loginByPhone
      - /api-docs
      - /doc.html
      - /druid
      - /swagger-resources
      - /webjars
      - /swagger
    log-end:
      - /_login


token:
  resolver:
    # igrone:
    #     mercodes: 666666
    body:
      enable: true

flowRules:
  '[
    {
        "resource":"ydjia-merchant-platform",
        "resourceMode":0,
        "count":1000,
        "intervalSec":1,
        "paramItem":{
            "parseStrategy":2,
            "fieldName":"merCode"
        }
    },
    {
       
        "resource":"hydee-business-order-web",
        "resourceMode":0,
        "count":100,
        "intervalSec":1,
        "paramItem":{
            "parseStrategy":2,
            "fieldName":"merCode"
        }
    },
    {
        "resource":"data_sync_third_callback",
        "resourceMode":1,
        "count":2,
        "intervalSec":10
    }
  ]'
apiDefinitions:
  '[
    {
        "apiName":"data_sync_third_callback",
        "predicateItems":[
            {
                "pattern":"/data-sync/third/callback/27/selectStock/**",
                "matchStrategy":1
            }
        ]
    }
  ]'

# logging:
#   file: ${user.dir}/logs/${spring.application.name}.info.log
#   level:
#     root: INFO
# logs:
#   info:
#     file: ${user.dir}/logs/${spring.application.name}.info.log
#     max-history: 7
#   error:
#     file: ${user.dir}/logs/${spring.application.name}.error.log
#     max-history: 7

safe-center:
  gateway-channel: B
  auth:
    enable:
      list: yxt-safe-center
# 动态路由功能开关
dynamic:
  enable: true


grey:
  enable: true
  local-mappings:
    '[(.+)]': $1.svc.k8s.dev.hxyxt.com
    'yxt-safe-center': localhost:8080


gateway:
  parameter:
    transform:
      enabled: false  # 默认关闭，需要时开启
      # 系统参数配置 - 请求和响应转换共用
      system:
        parameters:
          # 示例系统参数
          sysParam: "fsfy97fuhjf6u"
          appVersion: "1.0.0"
          environment: "production"
          systemId: "gateway-system"